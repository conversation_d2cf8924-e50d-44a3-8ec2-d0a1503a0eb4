/* =============================================
     Step 1: 甘特图基础样式系统
     ============================================= */

/* CSS 变量定义 - 支持主题切换 */
:root {
  /* 颜色变量 */
  --gantt-primary: #4a90e2;
  --gantt-success: #7ed321;
  --gantt-warning: #f5a623;
  --gantt-danger: #d0021b;
  --gantt-gray-50: #f9fafb;
  --gantt-gray-100: #f3f4f6;
  --gantt-gray-200: #e5e7eb;
  --gantt-gray-300: #d1d5db;
  --gantt-gray-400: #9ca3af;
  --gantt-gray-500: #6b7280;
  --gantt-gray-600: #4b5563;
  --gantt-gray-700: #374151;
  --gantt-gray-800: #1f2937;
  --gantt-gray-900: #111827;

  /* 甘特图浅色系 */
  --gantt-primary-25: #fefefe;
  --gantt-primary-50: #fdfcf9;
  --gantt-primary-75: #fbf9f4;
  --gantt-primary-100: #f8f5ef;
  --gantt-primary-200: #f3f0e8;
  --gantt-primary-300: #ede8dd;
  --gantt-primary-400: #e6dfd2;
  --gantt-primary-500: #ddd4c5;
  --gantt-primary-600: #d1c7b6;
  --gantt-primary-700: #c4b8a5;
  --gantt-primary-800: #b5a794;

  /* 辅助色系 */
  --gantt-secondary-50: #fafafa;
  --gantt-secondary-100: #f5f5f5;
  --gantt-secondary-200: #eeeeee;
  --gantt-secondary-300: #e0e0e0;
  --gantt-secondary-400: #bdbdbd;
  --gantt-secondary-500: #9e9e9e;
  --gantt-secondary-600: #757575;
  --gantt-secondary-700: #616161;

  /* 状态色系 - 浅色版本 */
  --gantt-success-100: #dcfce7;
  --gantt-success-200: #bbf7d0;
  --gantt-success-500: #22c55e;
  --gantt-success-600: #16a34a;
  --gantt-warning-100: #fef3c7;
  --gantt-warning-200: #fde68a;
  --gantt-warning-500: #eab308;
  --gantt-warning-600: #ca8a04;
  --gantt-error-100: #fee2e2;
  --gantt-error-200: #fecaca;
  --gantt-error-500: #ef4444;
  --gantt-error-600: #dc2626;

  /* 尺寸变量 */
  --gantt-header-height: 60px;
  --gantt-footer-height: 30px;
  --gantt-row-height: 40px;
  --gantt-splitter-width: 4px;
  --gantt-left-panel-width: 400px;

  /* 字体变量 */
  --gantt-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --gantt-font-size-sm: 12px;
  --gantt-font-size-base: 14px;
  --gantt-font-size-lg: 16px;

  /* 阴影和圆角 */
  --gantt-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --gantt-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --gantt-radius-sm: 4px;
  --gantt-radius: 6px;
  --gantt-radius-lg: 8px;
}

/* 基础重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
  font-family: var(--gantt-font-family);
}

/* =============================================
     甘特图容器
     ============================================= */
.gantt-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  font-family: var(--gantt-font-family);
  font-size: var(--gantt-font-size-base);
  color: var(--gantt-gray-700);
  background-color: white;
  border: 1px solid var(--gantt-gray-200);
  border-radius: var(--gantt-radius);
  overflow: hidden;
  box-shadow: var(--gantt-shadow);
}

/* =============================================
     头部工具栏
     ============================================= */
.gantt-header {
  height: var(--gantt-header-height);
  background-color: var(--gantt-gray-50);
  border-bottom: 1px solid var(--gantt-gray-200);
  flex-shrink: 0;
}

.gantt-toolbar {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.gantt-view-controls {
  display: flex;
  gap: 2px;
  background-color: var(--gantt-gray-100);
  border-radius: var(--gantt-radius-sm);
  padding: 2px;
}

.gantt-actions {
  display: flex;
  gap: 8px;
}

.gantt-btn {
  padding: 6px 12px;
  border: 1px solid var(--gantt-gray-300);
  background-color: white;
  color: var(--gantt-gray-700);
  border-radius: var(--gantt-radius-sm);
  cursor: pointer;
  font-size: var(--gantt-font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
}

.gantt-btn:hover {
  background-color: var(--gantt-gray-50);
  border-color: var(--gantt-gray-400);
  transform: translateY(-1px);
}

.gantt-btn:active {
  transform: translateY(0);
  color: #409EFF;
}

.gantt-btn.active {
  background-color: var(--gantt-primary);
  color: #409EFF;
  font-weight: 700;
  border-color: var(--gantt-primary);
}

.gantt-view-controls .gantt-btn {
  border: none;
  background-color: transparent;
  border-radius: var(--gantt-radius-sm);
}

.gantt-view-controls .gantt-btn:hover {
  background-color: var(--gantt-gray-200);
}

.gantt-view-controls .gantt-btn.active {
  background-color: white;
  box-shadow: var(--gantt-shadow-sm);
}

/* =============================================
     主体区域
     ============================================= */
.gantt-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧面板 */
.gantt-left-panel {
  width: var(--gantt-left-panel-width);
  background-color: white;
  border-right: 1px solid var(--gantt-gray-200);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* 分割器 */
.gantt-splitter {
  width: var(--gantt-splitter-width);
  background-color: var(--gantt-gray-200);
  cursor: col-resize;
  flex-shrink: 0;
  position: relative;
  transition: background-color 0.2s ease;
}

.gantt-splitter:hover {
  background-color: var(--gantt-primary);
}

.gantt-splitter::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 2px;
  height: 20px;
  background-color: var(--gantt-gray-400);
  transform: translate(-50%, -50%);
  transition: background-color 0.2s ease;
}

.gantt-splitter:hover::after {
  background-color: white;
}

/* 右侧面板 */
.gantt-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* =============================================
     表格区域
     ============================================= */
.gantt-table-header {
  height: calc(var(--gantt-row-height) * 1.5); /* 与时间轴头部高度保持一致 */
  background: linear-gradient(135deg, var(--gantt-primary-50) 0%, var(--gantt-secondary-50) 100%);
  border-bottom: 1px solid var(--gantt-primary-200);
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: var(--gantt-font-size-sm);
  /* color: var(--gantt-secondary-700); */
  color: var(--gantt-gray-800);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.gantt-table-header-cell {
  padding: 8px 12px;
  border-right: 1px solid var(--gantt-secondary-200);
  flex-shrink: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
  position: relative;
  cursor: default;
  transition: all 0.2s ease;
  background: var(--gantt-primary-50);
  border-bottom: 1px solid var(--gantt-primary-200);
  user-select: none;
  height: 100%;
}

.gantt-table-header-cell:hover,
.gantt-table-header-cell.hover {
  background: var(--gantt-primary-100);
  color: var(--gantt-secondary-700);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border-bottom-color: var(--gantt-primary-300);
}

.gantt-table-header-cell.sortable {
  cursor: pointer;
}

.gantt-table-header-cell.sortable:hover {
  background: var(--gantt-primary-200);
  color: var(--gantt-secondary-700);
}

.gantt-table-header-cell.sorted {
  background: var(--gantt-primary-200);
  color: var(--gantt-secondary-700);
  font-weight: 700;
  border-bottom: 2px solid var(--gantt-primary-400);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.gantt-table-header-cell.first-column {
  border-left: none;
}

.gantt-table-header-cell.last-column {
  border-right: none;
}

.gantt-table-header-cell.resizing {
  background: var(--gantt-primary-300);
  cursor: col-resize;
  border-bottom-color: var(--gantt-primary-500);
}

/* 头部内容布局 */
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.header-icon {
  font-size: 14px;
  color: var(--gantt-secondary-500);
  flex-shrink: 0;
}

.header-title {
  font-size: var(--gantt-font-size-sm);
  font-weight: 600;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.sort-indicator {
  display: flex;
  align-items: center;
  margin-left: auto;
  flex-shrink: 0;
}

.sort-icon {
  font-size: 12px;
  color: var(--gantt-secondary-400);
  transition: all 0.2s ease;
  font-style: normal;
  font-weight: bold;
}

.gantt-table-header-cell.sortable:hover .sort-icon {
  color: var(--gantt-secondary-600);
  transform: scale(1.1);
}

.gantt-table-header-cell.sorted .sort-icon {
  color: var(--gantt-secondary-700);
}

.sort-icon.sort-asc {
  color: var(--gantt-success-500);
}

.sort-icon.sort-desc {
  color: var(--gantt-error-500);
}

/* 列宽调整器 */
.column-resizer {
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  transition: all 0.2s ease;
  z-index: 10;
  border-radius: 2px;
}

.column-resizer:hover {
  background: var(--gantt-primary-400);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  width: 6px;
  right: -3px;
}

.gantt-table-header-cell.resizing .column-resizer {
  background: var(--gantt-primary-500);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.15);
  width: 6px;
  right: -3px;
}

.gantt-table-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.gantt-table-row {
  height: var(--gantt-row-height);
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--gantt-gray-100);
  transition: background-color 0.15s ease;
}

.gantt-table-row:hover {
  background-color: var(--gantt-gray-50);
}

.gantt-table-cell {
  padding: 8px 12px;
  border-right: 1px solid var(--gantt-gray-100);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--gantt-font-size-sm);
  flex-shrink: 0;
}

/* =============================================
     时间轴区域
     ============================================= */
.gantt-timeline-header {
  height: calc(var(--gantt-row-height) * 1.5); /* 增加高度以容纳两行 */
  background: linear-gradient(135deg, var(--gantt-primary-50) 0%, var(--gantt-secondary-50) 100%);
  border-bottom: 1px solid var(--gantt-primary-200);
  border-left: 1px solid var(--gantt-primary-200);
  overflow-x: hidden; /* 允许内部元素处理横向滚动 */
  overflow-y: hidden; /* 禁止纵向滚动 */
  position: relative;
  display: flex;
  align-items: stretch;
  padding: 0;
  font-weight: 600;
  font-size: var(--gantt-font-size-sm);
  color: var(--gantt-secondary-700);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 时间刻度容器 - 全渲染模式支持滚动 */
.gantt-timeline-scales {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: auto; /* 支持横向滚动 */
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  /* 优化全渲染性能 */
  will-change: transform;
  contain: layout style paint;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* 隐藏滚动条 - Webkit */
.gantt-timeline-scales::-webkit-scrollbar {
  display: none;
}

/* 顶部行容器 */
.timeline-scales-top-row {
  position: relative;
  height: 50%;
  width: 100%;
  border-bottom: 1px solid var(--gantt-primary-200);
  background: var(--gantt-primary-75);
}

/* 底部行容器 */
.timeline-scales-bottom-row {
  position: relative;
  height: 50%;
  width: 100%;
  background: var(--gantt-primary-50);
}

/* 单行容器（用于年视图） */
.timeline-scales-single-row {
  position: relative;
  height: 100%;
  width: 100%;
  background: var(--gantt-primary-50);
  display: flex;
  align-items: center;
}

/* 顶部行刻度组 */
.timeline-scale-group.top-row {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--gantt-primary-200);
  background-color: transparent;
  transition: all 0.2s ease;
  min-width: 40px;
  padding: 0 8px;
  box-sizing: border-box;
}

.timeline-scale-group.top-row:hover {
  background: var(--gantt-primary-100);
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.scale-group-label {
  font-size: var(--gantt-font-size-sm);
  font-weight: 600;
  color: var(--gantt-secondary-700);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;
  text-align: center;
  line-height: 1.2;
}

/* 底部行时间刻度项 */
.timeline-scale.bottom-row {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--gantt-secondary-200);
  background-color: transparent;
  transition: all 0.2s ease;
  min-width: 20px;
  padding: 0 2px;
  box-sizing: border-box;
  overflow: hidden;
}

.timeline-scale.bottom-row:hover {
  background: var(--gantt-primary-100);
  transform: translateY(-1px);
}

/* 主要刻度 */
.timeline-scale.bottom-row.major {
  border-right: 1px solid var(--gantt-primary-300);
  background: var(--gantt-primary-75);
  border-bottom: 1px solid var(--gantt-primary-400);
}

.timeline-scale.bottom-row.major:hover {
  background: var(--gantt-primary-200);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 次要刻度 */
.timeline-scale.bottom-row.minor {
  border-right: 1px solid var(--gantt-secondary-200);
  opacity: 0.9;
}

/* 底部行刻度标签 */
.timeline-scale.bottom-row .scale-label {
  font-size: var(--gantt-font-size-xs);
  color: var(--gantt-secondary-600);
  overflow: hidden;
  user-select: none;
  pointer-events: none;
  text-align: center;
  line-height: 1.2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2px;
  box-sizing: border-box;
}

.timeline-scale.bottom-row.major .scale-label {
  font-weight: 600;
  color: var(--gantt-secondary-700);
  font-size: var(--gantt-font-size-sm);
}

/* 日期数字样式 */
.timeline-scale.bottom-row .scale-label .date-number {
  font-size: var(--gantt-font-size-sm);
  font-weight: 600;
  color: var(--gantt-secondary-700);
  line-height: 1;
  margin-bottom: 1px;
}

.timeline-scale.bottom-row.major .scale-label .date-number {
  font-size: var(--gantt-font-size-base);
  font-weight: 700;
}

/* 周几标签样式 */
.timeline-scale.bottom-row .scale-label .weekday-label {
  font-size: 9px;
  font-weight: 400;
  color: var(--gantt-secondary-500);
  line-height: 1;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.timeline-scale.bottom-row.major .scale-label .weekday-label {
  font-size: 10px;
  color: var(--gantt-secondary-600);
  font-weight: 500;
}

/* 年份标签样式 */
.timeline-scale.bottom-row .scale-label .year-label {
  font-size: 8px;
  font-weight: 400;
  color: var(--gantt-secondary-400);
  line-height: 1;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.timeline-scale.bottom-row.major .scale-label .year-label {
  font-size: 9px;
  color: var(--gantt-secondary-500);
  font-weight: 500;
}

/* 周范围标签样式 */
.timeline-scale.bottom-row .scale-label .week-range {
  font-size: 8px;
  font-weight: 400;
  color: var(--gantt-secondary-500);
  line-height: 1;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.timeline-scale.bottom-row.major .scale-label .week-range {
  font-size: 9px;
  color: var(--gantt-secondary-600);
  font-weight: 500;
}

/* 周标签样式 */
.timeline-scale.bottom-row .scale-label .week-label {
  font-size: 8px;
  font-weight: 400;
  color: var(--gantt-secondary-500);
  line-height: 1;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.timeline-scale.bottom-row.major .scale-label .week-label {
  font-size: 9px;
  color: var(--gantt-secondary-600);
  font-weight: 500;
}

/* 年份后缀样式 */
.timeline-scale.bottom-row .scale-label .year-suffix {
  font-size: 8px;
  font-weight: 400;
  color: var(--gantt-secondary-400);
  line-height: 1;
  opacity: 0.6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.timeline-scale.bottom-row.major .scale-label .year-suffix {
  font-size: 9px;
  color: var(--gantt-secondary-500);
  font-weight: 500;
}

/* 单行模式下的时间刻度样式 */
.timeline-scales-single-row .timeline-scale.bottom-row {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--gantt-secondary-200);
  background-color: transparent;
  transition: all 0.2s ease;
  min-width: 60px;
  padding: 0 8px;
  box-sizing: border-box;
  overflow: hidden;
}

.timeline-scales-single-row .timeline-scale.bottom-row:hover {
  background: var(--gantt-primary-100);
  transform: translateY(-1px);
}

.timeline-scales-single-row .timeline-scale.bottom-row.major {
  border-right: 1px solid var(--gantt-primary-300);
  background: var(--gantt-primary-75);
  border-bottom: none;
}

.timeline-scales-single-row .timeline-scale.bottom-row.major:hover {
  background: var(--gantt-primary-200);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 单行模式下的标签样式 */
.timeline-scales-single-row .timeline-scale.bottom-row .scale-label {
  font-size: var(--gantt-font-size-base);
  color: var(--gantt-secondary-700);
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  box-sizing: border-box;
}

.timeline-scales-single-row .timeline-scale.bottom-row.major .scale-label {
  font-size: var(--gantt-font-size-lg);
  font-weight: 700;
  color: var(--gantt-secondary-800);
}

.timeline-scales-single-row .timeline-scale.bottom-row .scale-label .date-number {
  font-size: var(--gantt-font-size-lg);
  font-weight: 700;
  color: var(--gantt-secondary-800);
  line-height: 1;
  margin-bottom: 2px;
}

.timeline-scales-single-row .timeline-scale.bottom-row .scale-label .year-suffix {
  font-size: var(--gantt-font-size-sm);
  font-weight: 500;
  color: var(--gantt-secondary-600);
  line-height: 1;
  opacity: 0.8;
}

/* 周几显示样式 */
.timeline-scale.bottom-row .scale-label .weekday {
  font-size: 10px;
  color: var(--gantt-secondary-500);
  margin-top: 1px;
}

.timeline-scale.bottom-row.major .scale-label .weekday {
  color: var(--gantt-secondary-600);
}

/* 可见状态 */
.timeline-scale.visible,
.timeline-scale-group.visible {
  opacity: 1;
}

.timeline-scale.buffered,
.timeline-scale-group.buffered {
  opacity: 0.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .gantt-timeline-header,
  .gantt-table-header {
    height: calc(var(--gantt-row-height) * 1.3); /* 移动端稍微减少高度 */
  }

  .gantt-table-header-cell {
    padding: 8px 6px; /* 移动端减少内边距 */
    font-size: var(--gantt-font-size-xs);
  }

  .header-content {
    gap: 4px; /* 移动端减少间距 */
  }

  .header-title {
    font-size: 11px;
  }

  .sort-icon {
    font-size: 10px;
  }

  .column-resizer {
    width: 6px; /* 移动端增加触摸区域 */
    right: -3px;
  }

  /* 移动端隐藏部分功能 */
  .gantt-table-header-cell .header-icon {
    display: none;
  }

  .gantt-table-header-cell:hover {
    transform: none; /* 移动端禁用悬停动画 */
  }

  .timeline-scale.bottom-row {
    min-width: 15px;
    padding: 0 1px;
  }

  .timeline-scale.bottom-row .scale-label {
    font-size: 10px;
  }

  .timeline-scale.bottom-row .scale-label .date-number {
    font-size: 11px;
  }

  .timeline-scale.bottom-row .scale-label .weekday-label {
    font-size: 8px;
  }

  .timeline-scale.bottom-row.major .scale-label .date-number {
    font-size: 12px;
  }

  .timeline-scale.bottom-row.major .scale-label .weekday-label {
    font-size: 9px;
  }

  .timeline-scale.bottom-row .scale-label .year-label {
    font-size: 7px;
  }

  .timeline-scale.bottom-row.major .scale-label .year-label {
    font-size: 8px;
  }

  .timeline-scale.bottom-row .scale-label .week-range {
    font-size: 7px;
  }

  .timeline-scale.bottom-row.major .scale-label .week-range {
    font-size: 8px;
  }

  .timeline-scale.bottom-row .scale-label .week-label {
    font-size: 7px;
  }

  .timeline-scale.bottom-row.major .scale-label .week-label {
    font-size: 8px;
  }

  .timeline-scale.bottom-row .scale-label .year-suffix {
    font-size: 7px;
  }

  .timeline-scale.bottom-row.major .scale-label .year-suffix {
    font-size: 8px;
  }

  /* 单行模式移动端适配 */
  .timeline-scales-single-row .timeline-scale.bottom-row {
    min-width: 50px;
    padding: 0 4px;
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label {
    font-size: var(--gantt-font-size-sm);
    padding: 2px;
  }

  .timeline-scales-single-row .timeline-scale.bottom-row.major .scale-label {
    font-size: var(--gantt-font-size-base);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label .date-number {
    font-size: var(--gantt-font-size-base);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label .year-suffix {
    font-size: var(--gantt-font-size-xs);
  }

  .scale-group-label {
    font-size: 11px;
  }

  .timeline-scale-group.top-row {
    min-width: 30px;
    padding: 0 4px;
  }
}

/* 浅色主题（默认） */
.gantt-chart,
.gantt-chart.light-theme {
  /* 确保浅色主题的基础样式 */
  background-color: var(--gantt-background);
  color: var(--gantt-text);
}

/* 深色主题支持 - 通过类名控制 */
.gantt-chart.dark-theme {
  .gantt-table-header {
    background: var(--gantt-gray-800);
    border-bottom-color: var(--gantt-gray-600);
    color: var(--gantt-gray-200);
  }

  .gantt-timeline-header {
    background: var(--gantt-gray-800);
    border-bottom-color: var(--gantt-gray-600);
    color: var(--gantt-gray-200);
  }

  .gantt-table-header-cell {
    background: var(--gantt-gray-750);
    border-bottom-color: var(--gantt-gray-600);
    color: var(--gantt-gray-200);
    border-right-color: var(--gantt-gray-600);
  }

  .gantt-table-header-cell:hover {
    background: var(--gantt-gray-700);
    color: var(--gantt-gray-100);
  }

  .gantt-table-header-cell.sorted {
    background: var(--gantt-gray-700);
    color: var(--gantt-gray-100);
    border-bottom-color: var(--gantt-gray-500);
  }

  .timeline-scales-top-row {
    background: var(--gantt-gray-750);
    border-bottom-color: var(--gantt-gray-600);
  }

  .timeline-scales-bottom-row {
    background: var(--gantt-gray-800);
  }

  .scale-group-label {
    color: var(--gantt-gray-200);
  }

  .timeline-scale.bottom-row .scale-label {
    color: var(--gantt-gray-300);
  }

  .timeline-scale.bottom-row.major .scale-label {
    color: var(--gantt-gray-200);
  }

  .timeline-scale.bottom-row.major {
    background: var(--gantt-gray-700);
    border-bottom-color: var(--gantt-gray-500);
  }

  .header-icon {
    color: var(--gantt-gray-400);
  }

  .sort-icon {
    color: var(--gantt-gray-400);
  }

  .column-resizer:hover {
    background: var(--gantt-gray-500);
  }

  .gantt-table-header-cell.resizing .column-resizer {
    background: var(--gantt-gray-400);
  }

  /* 单行模式深色主题 */
  .timeline-scales-single-row {
    background: var(--gantt-gray-800);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row {
    border-right-color: var(--gantt-gray-600);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row:hover {
    background: var(--gantt-gray-700);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row.major {
    background: var(--gantt-gray-700);
    border-right-color: var(--gantt-gray-500);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row.major:hover {
    background: var(--gantt-gray-600);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label {
    color: var(--gantt-gray-200);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row.major .scale-label {
    color: var(--gantt-gray-100);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label .date-number {
    color: var(--gantt-gray-100);
  }

  .timeline-scales-single-row .timeline-scale.bottom-row .scale-label .year-suffix {
    color: var(--gantt-gray-300);
  }
}

.gantt-chart-body {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: white;
}

/* SVG 样式 */
.gantt-svg {
  /* 不设置固定宽度，让JavaScript动态控制 */
  height: 100%;
  display: block;
  min-width: 1000px;
  min-height: 400px;
}

/* =============================================
     底部状态栏
     ============================================= */
.gantt-footer {
  height: var(--gantt-footer-height);
  background-color: var(--gantt-gray-50);
  border-top: 1px solid var(--gantt-gray-200);
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-shrink: 0;
}

.gantt-status {
  display: flex;
  gap: 20px;
  font-size: var(--gantt-font-size-sm);
  color: var(--gantt-gray-500);
}

/* =============================================
     演示和占位内容
     ============================================= */
.demo-content {
  padding: 20px;
  text-align: center;
  color: var(--gantt-gray-500);
}

.demo-content h3 {
  margin: 0 0 16px 0;
  color: var(--gantt-primary);
  font-size: var(--gantt-font-size-lg);
}

.demo-content p {
  margin: 8px 0;
  font-size: var(--gantt-font-size-sm);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 16px 0;
}

.feature-list li {
  margin: 4px 0;
  padding: 4px 8px;
  background-color: var(--gantt-gray-50);
  border-radius: var(--gantt-radius-sm);
  font-size: var(--gantt-font-size-sm);
}

.feature-list li.completed {
  background-color: #e6f7e6;
  color: var(--gantt-success);
}

.feature-list li.completed::before {
  content: "✓ ";
  font-weight: bold;
}

/* =============================================
     滚动条样式
     ============================================= */
.gantt-table-body::-webkit-scrollbar,
.gantt-chart-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.gantt-table-body::-webkit-scrollbar-track,
.gantt-chart-body::-webkit-scrollbar-track {
  background: var(--gantt-gray-100);
}

.gantt-table-body::-webkit-scrollbar-thumb,
.gantt-chart-body::-webkit-scrollbar-thumb {
  background: var(--gantt-gray-300);
  border-radius: 4px;
}

.gantt-table-body::-webkit-scrollbar-thumb:hover,
.gantt-chart-body::-webkit-scrollbar-thumb:hover {
  background: var(--gantt-gray-400);
}

/* =============================================
     响应式设计
     ============================================= */
@media (max-width: 768px) {
  :root {
    --gantt-left-panel-width: 300px;
    --gantt-font-size-base: 13px;
    --gantt-font-size-sm: 11px;
  }

  .gantt-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 16px;
  }

  .gantt-header {
    height: auto;
    min-height: var(--gantt-header-height);
  }
}
